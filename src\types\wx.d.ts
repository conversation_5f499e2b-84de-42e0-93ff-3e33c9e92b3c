// 微信小程序类型声明

declare namespace WechatMiniprogram {
  interface CloudCallFunctionResult {
    result: {
      openid: string;
      [key: string]: any;
    };
    requestID: string;
    errMsg: string;
  }

  interface CloudCallFunctionOptions {
    name: string;
    data?: any;
    success?: (res: CloudCallFunctionResult) => void;
    fail?: (err: any) => void;
    complete?: (res: any) => void;
  }

  interface CloudInitOptions {
    env: string;
    traceUser?: boolean;
  }

  interface Cloud {
    init(options: CloudInitOptions): void;
    callFunction(options: CloudCallFunctionOptions): Promise<CloudCallFunctionResult>;
  }
}

declare const wx: {
  cloud: WechatMiniprogram.Cloud;
  [key: string]: any;
};
