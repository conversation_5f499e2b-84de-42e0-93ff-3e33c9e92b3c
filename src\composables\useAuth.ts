import { useUserStore } from '@/stores/user'
import { getWechatUserInfo, wechatLogin, type UserInfo } from '@/utils/auth'

export const useAuth = () => {
  const userStore = useUserStore()

  // 获取用户openid
  const getUserOpenid = () => {
    return userStore.openid
  }

  // 微信登录流程
  const login = async (): Promise<UserInfo> => {
    try {
      // 1. 微信登录获取code
      const token = await wechatLogin()
      userStore.setToken(token)

      // 2. 获取用户信息
      const userInfo = await getWechatUserInfo()
      userStore.setUserInfo(userInfo)

      return userInfo
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 退出登录
  const logout = () => {
    userStore.clearUserData()
  }

  // 检查登录状态
  const checkLoginStatus = () => {
    return userStore.isLoggedIn
  }

  // 检查是否有openid
  const checkAuthentication = () => {
    return userStore.isAuthenticated
  }

  return {
    // 状态
    userInfo: userStore.userInfo,
    token: userStore.token,
    openid: userStore.openid,
    isLoggedIn: userStore.isLoggedIn,
    isAuthenticated: userStore.isAuthenticated,

    // 方法
    getUserOpenid,
    login,
    logout,
    checkLoginStatus,
    checkAuthentication
  }
}
