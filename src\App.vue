<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/user";

onLaunch(() => {
  console.log("App Launch");

  // 初始化用户store
  const userStore = useUserStore();
  userStore.initFromStorage();

  // 初始化微信云开发
  wx.cloud.init({
    env: "cloud1-6gxijqna174103d8",
    traceUser: true,
  });

  // 调用云函数获取openid
  wx.cloud.callFunction({
    name: 'get_openId',
    success: (res: WechatMiniprogram.CloudCallFunctionResult) => {
      // 使用Pinia store存储用户openid
      userStore.setOpenid(res.result.openid);
      console.log('用户openid已保存:', res.result.openid);
    },
    fail: (err: any) => {
      console.error('获取openid失败:', err);
    }
  });
});

onShow(() => {
  console.log("App Show");
});

onHide(() => {
  console.log("App Hide");
});
</script>
<style></style>
