<template>
  <view class="openid-example">
    <view class="info-card">
      <text class="title">用户认证状态</text>
      
      <view class="status-item">
        <text class="label">登录状态:</text>
        <text class="value" :class="{ 'success': isLoggedIn, 'error': !isLoggedIn }">
          {{ isLoggedIn ? '已登录' : '未登录' }}
        </text>
      </view>
      
      <view class="status-item">
        <text class="label">认证状态:</text>
        <text class="value" :class="{ 'success': isAuthenticated, 'error': !isAuthenticated }">
          {{ isAuthenticated ? '已认证' : '未认证' }}
        </text>
      </view>
      
      <view class="status-item" v-if="openid">
        <text class="label">用户OpenID:</text>
        <text class="value openid">{{ openid }}</text>
      </view>
      
      <view class="status-item" v-if="userInfo">
        <text class="label">用户昵称:</text>
        <text class="value">{{ userInfo.nickname }}</text>
      </view>
      
      <view class="actions">
        <button 
          class="btn" 
          :class="{ 'primary': !isLoggedIn, 'danger': isLoggedIn }"
          @click="handleAuth"
        >
          {{ isLoggedIn ? '退出登录' : '登录' }}
        </button>
        
        <button class="btn secondary" @click="refreshOpenid" v-if="isAuthenticated">
          刷新OpenID
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'

// 使用认证composable
const auth = useAuth()

// 解构响应式状态
const { userInfo, openid, isLoggedIn, isAuthenticated } = auth

// 处理认证
const handleAuth = async () => {
  if (isLoggedIn.value) {
    auth.logout()
    uni.showToast({
      title: '已退出登录',
      icon: 'success'
    })
  } else {
    try {
      uni.showLoading({ title: '登录中...' })
      await auth.login()
      uni.hideLoading()
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error) {
      uni.hideLoading()
      uni.showToast({
        title: '登录失败',
        icon: 'error'
      })
    }
  }
}

// 刷新OpenID（重新调用云函数）
const refreshOpenid = () => {
  uni.showLoading({ title: '获取中...' })
  
  wx.cloud.callFunction({
    name: 'get_openId',
    success: (res: WechatMiniprogram.CloudCallFunctionResult) => {
      auth.setOpenid(res.result.openid)
      uni.hideLoading()
      uni.showToast({
        title: 'OpenID已更新',
        icon: 'success'
      })
    },
    fail: (err: any) => {
      uni.hideLoading()
      console.error('获取openid失败:', err)
      uni.showToast({
        title: '获取失败',
        icon: 'error'
      })
    }
  })
}
</script>

<style scoped>
.openid-example {
  padding: 32rpx;
}

.info-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 32rpx;
  display: block;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e2e8f0;
}

.label {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #2d3748;
}

.value.success {
  color: #38a169;
  font-weight: bold;
}

.value.error {
  color: #e53e3e;
  font-weight: bold;
}

.value.openid {
  font-family: monospace;
  font-size: 24rpx;
  background: #f7fafc;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.actions {
  margin-top: 32rpx;
  display: flex;
  gap: 16rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.btn.primary {
  background: #7fb069;
  color: white;
}

.btn.danger {
  background: #e53e3e;
  color: white;
}

.btn.secondary {
  background: #edf2f7;
  color: #4a5568;
}
</style>
