// 用户认证相关工具函数

export interface UserInfo {
  id: string;
  nickname: string;
  avatar: string;
  gender: number;
  city: string;
  province: string;
  country: string;
  language: string;
  openid?: string; // 添加openid字段
}

// 获取微信用户信息
export const getWechatUserInfo = (): Promise<UserInfo> => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.getUserProfile({
      desc: "用于完善用户资料",
      success: (res) => {
        const userInfo: UserInfo = {
          id: generateUserId(),
          nickname: res.userInfo.nickName || "微信用户",
          avatar: res.userInfo.avatarUrl || "/static/icons/avatar.png",
          gender: res.userInfo.gender || 0,
          city: res.userInfo.city || "",
          province: res.userInfo.province || "",
          country: res.userInfo.country || "",
          language: res.userInfo.language || "zh_CN",
        };
        // 保存用户信息到本地存储
        uni.setStorageSync("userInfo", userInfo);
        resolve(userInfo);
      },
      fail: (err) => {
        console.error("获取用户信息失败:", err);
        reject(err);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回模拟数据
    const mockUserInfo: UserInfo = {
      id: generateUserId(),
      nickname: "多肉爱好者",
      avatar: "/static/icons/avatar.png",
      gender: 1,
      city: "深圳",
      province: "广东",
      country: "中国",
      language: "zh_CN",
    };
    uni.setStorageSync("userInfo", mockUserInfo);
    resolve(mockUserInfo);
    // #endif
  });
};

// 微信登录
export const wechatLogin = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.login({
      provider: "weixin",
      success: (res) => {
        if (res.code) {
          // 这里应该将code发送到后端服务器，换取openid和session_key
          console.log("登录成功，code:", res.code);
          // 模拟返回token
          const token = "mock_token_" + Date.now();
          uni.setStorageSync("token", token);
          resolve(token);
        } else {
          reject(new Error("登录失败"));
        }
      },
      fail: (err) => {
        console.error("登录失败:", err);
        reject(err);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回模拟token
    const mockToken = "mock_token_" + Date.now();
    uni.setStorageSync("token", mockToken);
    resolve(mockToken);
    // #endif
  });
};

// 生成用户ID
const generateUserId = (): string => {
  return (
    "user_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11)
  );
};

// 获取本地存储的用户信息
export const getStoredUserInfo = (): UserInfo | null => {
  try {
    const userInfo = uni.getStorageSync("userInfo");
    return userInfo || null;
  } catch (error) {
    console.error("获取本地用户信息失败:", error);
    return null;
  }
};

// 获取本地存储的token
export const getStoredToken = (): string | null => {
  try {
    const token = uni.getStorageSync("token");
    return token || null;
  } catch (error) {
    console.error("获取本地token失败:", error);
    return null;
  }
};

// 清除用户信息
export const clearUserInfo = (): void => {
  try {
    uni.removeStorageSync("userInfo");
    uni.removeStorageSync("token");
  } catch (error) {
    console.error("清除用户信息失败:", error);
  }
};

// 检查是否已登录
export const isLoggedIn = (): boolean => {
  const token = getStoredToken();
  const userInfo = getStoredUserInfo();
  return !!(token && userInfo);
};
